# Date Picker Usage Guide

The `BaseFormDatePicker` component has been implemented using the vue-datepicker plugin and integrates seamlessly with the existing form system.

## Automatic Usage in Forms

The date picker will automatically be used when a form field has `type: "date"`. No additional configuration is needed.

### Example: Form Field with Date Type

```vue
<template>
	<BaseForm v-slot="{values}">
		<BaseFormField :item="dateField" v-slot="{errorMessage, floatingLabel, isTouched}">
			<p class="field" :class="['field-' + dateField.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
				<BaseFormInput :id="dateField.name" />
				<label :for="dateField.name">
					<BaseCmsLabel :code="dateField.name" />
				</label>
				<span class="error" v-show="errorMessage" v-html="errorMessage" />
			</p>
		</BaseFormField>
	</BaseForm>
</template>

<script setup>
	const dateField = ref({
		name: 'event_date',
		type: 'date',  // This triggers the date picker
		value: '',
		validation: [
			{
				type: 'not_empty',
				value: null,
				error: 'error_not_empty'
			}
		]
	});
</script>
```

## Direct Component Usage

You can also use the `BaseFormDatePicker` component directly:

```vue
<template>
	<BaseFormDatePicker 
		v-model="selectedDate"
		name="my_date"
		placeholder="Select a date"
		:min-date="'01.01.2020'"
		:max-date="new Date()"
		@change="onDateChange"
	/>
</template>

<script setup>
	const selectedDate = ref('');
	
	function onDateChange(newDate) {
		console.log('Date changed:', newDate);
	}
</script>
```

## Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `id` | String | - | Field ID |
| `name` | String | - | Field name |
| `class` | String | - | CSS classes |
| `value` | String/Number/Date | - | Initial value |
| `field` | Object | - | Field configuration object |
| `placeholder` | String | - | Placeholder text |
| `locale` | String | 'hr-HR' | Date locale |
| `clearable` | Boolean | true | Show clear button |
| `minDate` | String/Date | '01.01.1900' | Minimum selectable date |
| `maxDate` | String/Date | Current date | Maximum selectable date |

## Date Formats

The component handles multiple date formats:

- **Croatian format**: `DD.MM.YYYY.` (e.g., "25.12.2023.")
- **ISO format**: `YYYY-MM-DD` (e.g., "2023-12-25")
- **Unix timestamp**: Number (e.g., 1703462400)
- **Date object**: JavaScript Date object

The component always outputs dates in Croatian format (`DD.MM.YYYY.`) for form submission.

## Events

| Event | Description | Payload |
|-------|-------------|---------|
| `update:modelValue` | Emitted when date changes | Formatted date string |
| `change` | Emitted when date changes | Formatted date string |
| `focus` | Emitted when input gains focus | Event object |
| `blur` | Emitted when input loses focus | Event object |

## Integration with Form Validation

The date picker integrates with vee-validate and supports all standard validation rules:

```javascript
const dateField = {
	name: 'birth_date',
	type: 'date',
	validation: [
		{
			type: 'not_empty',
			value: null,
			error: 'error_not_empty'
		}
	]
};
```

## Styling

The component inherits the existing form styling and includes:

- Error state styling (red border when validation fails)
- Success state styling (green border when validation passes)
- Floating label support
- Consistent calendar styling with the rest of the application

## Common Use Cases

### 1. Birth Date Picker
```vue
<BaseFormDatePicker 
	v-model="birthDate"
	name="birth_date"
	:min-date="'01.01.1900'"
	:max-date="new Date()"
	:clearable="false"
/>
```

### 2. Event Date Picker
```vue
<BaseFormDatePicker 
	v-model="eventDate"
	name="event_date"
	:min-date="new Date()"
	placeholder="Select event date"
/>
```

### 3. Date Range (From Date)
```vue
<BaseFormDatePicker 
	v-model="fromDate"
	name="from_date"
	:max-date="toDate || new Date()"
	@change="updateToDateMin"
/>
```

## Notes

- The vue-datepicker plugin is already installed and configured
- The component automatically integrates with the existing form system
- Croatian locale is set by default
- Calendar icon is included in the input styling
- The component is fully responsive and mobile-friendly
