<template>
	<article class="cp" :class="[{'cp-unavailable': !item.is_available}, {'cp-list': mode == 'list', 'cp-list instashop': mode == 'instashop'}]">
		<div class="cp-col cp-col1">
			<template v-if="mode != 'bought_together' && mode != 'instashop'">
				<div class="cp-attr-container" v-if="itemsAttributes">
					<template v-for="(specialAttr, index) in itemsAttributes" :key="specialAttr.id">
						<div class="cp-attr" v-if="index < 3">
							<span class="cp-attr-title">{{ specialAttr.title }}</span>
							<span class="cp-attr-image"><BaseUiImage loading="lazy" :src="specialAttr.image_upload_path" default="/images/no-image-50.jpg" alt="<?php echo $item_attr['title']; ?>" width="50" height="50" /></span>
						</div>
					</template>
				</div>
			</template>

			<div class="cp-brand" v-if="item?.manufacturer_id?.length">
				<BaseUiImage loading="lazy" :src="item.manufacturer_main_image_upload_path" default="/images/no-image-50.jpg" />
			</div>

			<div class="cp-image" :class="{'instashop': mode == 'instashop'}">
				<!-- FIXME INTEG DK složiti još case kad je user b2b, kad je loyalty i pickup_only -->
				<div class="cp-badges">
					<template v-if="b2b">
						<span class="cp-badge cp-badge-discount" :class="{'special': itemsAttributes?.length}" v-if="((1 - item.price_b2b_custom / item.basic_price_b2b_custom) * 100).toFixed(0) > 0">-{{ ((1 - item.price_b2b_custom / item.basic_price_b2b_custom) * 100).toFixed(0) }}%</span>
					</template>
					<template v-else>
						<div class="cp-badge cp-badge-discount" v-if="item?.discount_percent_custom && item.discount_percent_custom > 0">%</div>
						<span class="cp-badge cp-badge-special" v-if="bestBuy"><BaseCmsLabel code="best_buy" tag="span" /></span>
						<BaseCmsLabel code="badge_new" tag="div" class="cp-badge cp-badge-new" v-if="item?.priority_details?.code == 'new' && !bestBuy" />
					</template>
				</div>
				<div class="cp-badges">
					<span class="cp-badge cp-badge-action cp-badge-pickup" v-if="item.type == 'pickup'">
						<span><BaseCmsLabel code="pickup_only" default="Samo u poslovnici" /> ({{ availablePickupLocations?.length }})</span>
					</span>
				</div>
				<!--
				<div class="cp-badges">
					<?php if(!empty($info['user_b2b']) AND $item['discount_percent_b2b_custom'] > 0 OR $item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
						<?php if (!empty($info['user_b2b'])): ?>
							<?php if ($item['discount_percent_b2b_custom'] > 0): ?>
								<span class="cp-badge cp-badge-discount<?php if($item['attributeitems_special']): ?> special<?php endif; ?>">-<?php echo (1 - ($item['price_b2b_custom'] / $item['basic_price_b2b_custom'])) * 100; ?>%</span>
							<?php endif; ?>
						<?php else: ?>
							<?php if($is_loyalty AND $item['loyalty_price'] < $item['basic_price']): ?>
								<span class="cp-badge cp-badge-discount">-<?php echo $loyalty_user['discount_percent']; ?>%</span>
							<?php else: ?>
								<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
									<span class="cp-badge cp-badge-discount">-<?php echo $item['discount_percent']; ?>%</span>
								<?php endif; ?>
							<?php endif; ?>
						<?php endif; ?>
					<?php elseif(!empty($item['attributeitems_special'])): ?>
						<span class="cp-badge cp-badge-special"><span><?php echo Arr::get($cmslabel, 'best_buy'); ?></span></span>
					<?php else: ?>
						<?php $product_priorities = Kohana::config('app.catalog.product_priorities'); ?>
						<?php $priority = Arr::get($product_priorities, $item['priority_2']); ?>
						<?php if ($priority): ?>
							<span class="cp-badge cp-badge-<?php echo Arr::get($priority, 'code'); ?><?php if($item['attributeitems_special']): ?> special<?php endif; ?>"><?php echo Arr::get($cmslabel, 'priority_' . Arr::get($priority, 'code')); ?></span>
						<?php endif; ?>
					<?php endif; ?>
					<?php if ($pickup_only):?>
						<?php $pickup_available_count = 0; ?>
						<?php foreach ($item['warehouses'] AS $location): ?>
							<?php $location_available_qty = (int)$location['available_qty']; ?>
							<?php if ($location_available_qty > 0): ?>
								<?php $pickup_available_count++; ?>
							<?php endif; ?>
						<?php endforeach; ?>
						<span class="cp-badge cp-badge-action cp-badge-pickup"><span><?php echo Arr::get($cmslabel, 'pickup_only', 'Samo u poslovnici'); ?> (<?php echo $pickup_available_count; ?>)</span></span>
					<?php endif; ?>
				</div>
				-->

				<NuxtLink :to="item.url_without_domain" class="cp-main-image" target="_parent">
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width120-height120']" default="/images/no-image-100.jpg" :title="item.main_image_title" :alt="item.main_image_description || item.title" v-if="mode == 'instashop'" />
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width300-height295']" default="/images/no-image-300.jpg" :title="item.main_image_title" :alt="item.main_image_description || item.title" v-else />
				</NuxtLink>
			</div>
		</div>
		<div class="cp-col cp-col2">
			<ClientOnly>
				<!-- Wishlist -->
				<CatalogSetWishlist :item="item" :mode="mode" v-if="mode != 'bought_together' && mode != 'wishlist'" />
			</ClientOnly>
			<div class="cp-cnt">
				<ClientOnly>
					<!-- Rating -->
					<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
						<div class="cp-rate cp-rate-cnt">
							<FeedbackRates :rates="item.feedback_rate_widget.rates" mode="cp-rates" v-if="item.feedback_rate_widget.rates_votes > 0" />
							<div class="cp-rate-counter" v-if="item.feedback_comment_widget?.comments > 0">
								<span class="num">({{ item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0 }})</span>
							</div>
						</div>
					</template>
				</ClientOnly>

				<NuxtLink :to="item.category_url_without_domain" class="cp-category" target="_parent">{{ item.category_title }}</NuxtLink>
				<div class="cp-title">
					<NuxtLink :to="item.url_without_domain" target="_parent">{{ item.title }}</NuxtLink>
				</div>

				<!-- FIXME INTEG DK složiti integraciju za bought_together
				<?php if($mode == 'bought_together'): ?>
					<div class="cp-bought-together-message product_message product_message_<?php echo $item['shopping_cart_code']; ?>" style="display: none"></div>
				<?php endif; ?>
				-->
			</div>

			<div class="cp-footer">
				<!-- FIXME INTEG DK složiti provjere za b2b i loyalty -->
				<div class="cp-price">
					<template v-if="b2b">
						<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
					</template>
					<template v-else>
						<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
							<template v-if="loyalty?.active && item.type != 'coupon'">
								<div class="old-price cp-old-price">
									<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
								</div>
								<div class="current-price red discount-price cp-discount-price" v-if="item.discount_percent > loyalty.discount_percent"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
								<div class="current-price red discount-price cp-discount-price" v-else><BaseUtilsFormatCurrency :price="item.loyalty_price" /></div>

								<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
									<template v-if="item.extra_price_lowest > 0">
										<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
									</template>
								</div>
							</template>
							<template v-else>
								<template v-if="item.price_custom > 0">
									<template v-if="item.discount_percent_custom > 0">
										<div class="old-price cp-old-price">
											<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
										</div>
										<div class="current-price red discount-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
										<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
											<template v-if="item.extra_price_lowest > 0">
												<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
											</template>
										</div>
									</template>
									<template v-else>
										<div class="current-price cp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
										<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'"></div>
									</template>
								</template>
							</template>
						</WebshopLoyalty>
						<!--
						<template v-if="hasLoyalty && item.loyalty_price < item.basic_price && item.type != 'coupon'">
							<div class="old-price cp-old-price">
								<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
							</div>
							<div class="current-price red discount-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.loyalty_price" /></div>
							<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
								<template v-if="item.extra_price_lowest > 0">
									<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
								</template>
							</div>
						</template>
						<template v-else>
							<template v-if="item.price_custom > 0">
								<template v-if="item.discount_percent_custom > 0">
									<div class="old-price cp-old-price">
										<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
									</div>
									<div class="current-price red discount-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
										<template v-if="item.extra_price_lowest > 0">
											<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
										</template>
									</div>
								</template>
								<template v-else>
									<div class="current-price cp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'"></div>
								</template>
							</template>
						</template>
						-->
					</template>
				</div>

				<div class="cp-addtocart" :class="{'cp-addtocart-single': item.available_qty <= item.package_qty}">
					<template v-if="item.is_available">
						<WebshopQty :quantity="item.quantity" :limit="item.available_quantity" :item="item" class="cp-qty" :class="{'cp-qty-single': item.available_qty <= item.package_qty}" />
						<template v-if="mode == 'bought_together'">
							<div class="cp-checkbox" v-show="item.id != current">
								<input type="checkbox" :name="item.shopping_cart_code" :value="item" :id="item.shopping_cart_code" v-model="checkedItems" :disabled="index == 0" />
								<label :for="item.shopping_cart_code"><BaseCmsLabel code="bought_together_label" tag="span" /></label>
							</div>
						</template>
						<template v-else>
							<BaseWebshopAddToCart :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: item.qty}" v-slot="{onAddToCart, loading}">
								<button :class="['btn btn-add btn-orange cp-btn-addtocart', {'loading': loading}]" @click="onAddToCart">
									<UiLoader v-if="loading" />
									<BaseCmsLabel code="add_to_cart" tag="span" />
								</button>
							</BaseWebshopAddToCart>
						</template>
					</template>
					<template v-else>
						<BaseCmsLabel code="unavailable" tag="span" class="cp-unavailable-label" />
						<NuxtLink :to="item.url_without_domain" class="btn cp-btn-detail"><span></span></NuxtLink>
					</template>
				</div>
				<!-- 
				<?php $package_qty = (!empty($item['package_qty'])) ? $item['package_qty'] : 1; ?>
				<div class="cp-addtocart<?php if($item['available_qty'] <= $package_qty): ?> cp-addtocart-single<?php endif; ?> <?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> pickup-not-visible<?php endif; ?><?php endif; ?>"<?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia-adtc<?php endif; ?>>
					<?php if($item['is_available']): ?>	
						<div class="cp-qty<?php if($item['available_qty'] <= $package_qty): ?> cp-qty-single<?php endif; ?>">
							<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '-', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" class="wp-btn-qty wp-btn-dec"><span class="toggle-icon"></span></a>
							<input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="wp-input-qty" value="<?php echo $package_qty; ?>" />
							<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '+', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" class="wp-btn-qty wp-btn-inc"><span class="toggle-icon"></span></a>
						</div>

						<?php if($mode == 'bought_together'): ?>
							<div class="cp-checkbox">
								<input type="hidden" name="price[<?php echo $item['shopping_cart_code']; ?>]" value="<?php echo $item['price_custom']; ?>" />
								<input type="checkbox"<?php if (!empty($item_id) AND $item_id == $item['id']): ?> disabled<?php endif; ?> name="product_special_list_recommendation" id="product_special_list_recommendation-<?php echo $item['shopping_cart_code']; ?>" value="<?php echo $item['shopping_cart_code']; ?>" onclick="javascript:cmswebshop.shopping_cart.calculate_special_list('recommendation');" checked="checked" />
								<label for="product_special_list_recommendation-<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></label>
							</div>
						<?php else: ?>
							<?php $ga4_code = $item['id'] . '_' . $item_list_id; ?>
							<a class="btn btn-orange cp-btn-addtocart<?php if($item['available_qty'] <= $package_qty): ?> cp-btn-addtocart-single<?php endif; ?>" data-ga4_code="<?php echo $ga4_code; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_cart'); ?>"  href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 1)">
								<span><?php echo Arr::get($cmslabel, 'add_to_cart'); ?></span>
							</a>
							<?php if($mode == 'instashop' AND $info['user_device'] == 'm'): ?>
								<span class="cp-add-success product_message product_message_<?php echo $item['shopping_cart_code']; ?>" style="display: none"></span>
							<?php endif; ?>
						<?php endif; ?>
					<?php else: ?>
						<span class="cp-unavailable-label"><?php echo Arr::get($cmslabel, 'unavailable'); ?></span>
						<a class="btn cp-btn-detail" href="<?php echo $item['url']; ?>"><span></span></a>
					<?php endif; ?>
				</div>
				-->
			</div>
		</div>
	</article>
</template>

<script setup>
	const {b2b, isLoggedIn, user, hasLoyalty} = useProfile();
	const props = defineProps({
		item: Object,
		mode: String,
		list: String,
		extraclass: String,
		itemListId: String,
		itemListName: String,
	});

	const itemsAttributes = computed(() => {
		if (!props.item.attributes_special) return [];
		return props.item.attributes_special.filter(item => item.attribute_code != 'posebne_oznake');
	});
	const bestBuy = computed(() => {
		if (!props.item.attributes_special) return false;
		return props.item.attributes_special.some(item => item.attribute_code === 'posebne_oznake' && item.code === 'best_buy');
	});
	const availablePickupLocations = computed(() => {
		return props.item.warehouses.filter(warehouse => warehouse.available_qty > 0);
	});
	//console.log(isLoggedIn.value);
	//const b2b = computed(() => isLoggedIn && user.value.b2b === '1')
</script>

<style lang="less" scoped>
	.cp-rates{
		display: flex;
		:deep(.icon-star), :deep(.icon-star-empty){
			position: relative; margin-right: 2px;
			&:before{.icon-star(); font: 12px/12px @fonti; color: #d5d9d3;}
		}
		:deep(.icon-star:before){color: @yellow;}
	}

	.cp-list.instashop{
		padding: 12px 16px 12px 8px;
		@media (min-width: @h){
			&:hover{box-shadow: none; border-color: @green;}
		}
		.cp-col1{width: 120px; margin-right: 15px;}
		.cp-image{
			height: 120px; align-items: center; justify-content: center;
			:deep(img){margin: 0;}
		}
		.cp-brand{
			top: 0; height: 18px;
			:deep(img){max-height: 18px;}
		}
		.cp-col2{margin: 0; flex-flow: row; flex-wrap: inherit;}
		.cp-addtocart:before{display: none;}
		.cp-footer{flex-flow: column; align-items: flex-end; padding-top: 35px; width: 150px; flex-grow: 0; flex-shrink: 0; margin-left: 35px;}
		.cp-btn-addtocart{
			position: relative; width: 44px;
			&:before{.icon-cart(); font: 23px/1 @fonti; color: @white; z-index: 1;}
			span{display: none;}
		}
		.cp-price{font-size: 14px; justify-content: flex-end; text-align: right;}
		.cp-wishlist{right: -5px;}
		:deep(.cp-wishlist-btn:after){font-size: 18px;}
		@media (max-width: @l){
			.cp-footer{width: 125px; margin-left: 15px;}
		}
		@media (max-width: @t){
			.cp-col2{flex-flow: column; align-items: flex-start; justify-content: flex-start;}
			.cp-footer{width: 100%; margin-left: 0; padding-top: 15px; flex-flow: row; align-items: center; justify-content: space-between;}
			.cp-price{justify-content: flex-start; text-align: left;}
		}
		@media (max-width: @instashopT){
			:deep(.cp-wishlist-btn span){display: none;}
		}
		@media (max-width: 800px){
			padding: 12px 16px 12px 12px;
			.cp-addtocart{height: 40px; width: auto;}
			.cp-btn-addtocart{
				flex-grow: 0; width: 40px; height: 40px;
				&:before{font-size: 20px;}
			}
			.cp-qty{width: 85px;}
			.cp-col1{width: 112px; margin-right: 12px;}
			.cp-rate{height: auto;}
			.cp-rate-counter{margin-top: 0;}
			.cp-category{font-size: 10px; padding: 1px 0 5px;}
			.cp-title{margin-top: 0;}
			.cp-price, .cp-addtocart{margin-top: 0;}
		}
	}
</style>
