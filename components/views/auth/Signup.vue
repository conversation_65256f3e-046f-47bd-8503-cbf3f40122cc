<template>
	<Body class="page-auth-signup page-auth-signup main-offset-sm page-auth page-signup" />
	<BaseCmsPage v-slot="{page}" :fetch-slug-segments="2">
		<AuthLoginRegisterLayout :page="page" mode="signup">
			<template #col1>
				<BaseAuthSignupForm class="form-label auth-form auth-signup-form" v-slot="{fields, errors, apiErrors, status, contentType, loading, values}" @load="onFormLoad">
					<template v-if="apiErrors?.length">
						<div class="global-error" v-for="apiError in apiErrors" :key="apiError">{{ apiError.field }}: {{ apiError.error }}</div>
					</template>
					<template v-if="Object.keys(errors)?.length">
						<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
					</template>
					{{values}}
					<!-- FIXME - provjeriti kako bi ovo mogli riješiti za country polje
					
					<?php
						$country_id = 1;
						$country_code = App::country_code();
						if (!empty($country_code)) {
							$country_id = Webshop::country_by_code($country_code);
						}
						$country_id = (!empty($customer_data['country'])) ? $customer_data['country'] : $country_id;
					?> -->

					<div class="reg-section">
						<BaseCmsLabel code="login_info" tag="div" class="a-subtitle" />

						<BaseAuthConfirmSignup v-if="contentType == 'confirmSignup'" v-slot="{status}" redirect="auth_login" :redirect-timeout="3000">
							<BaseCmsLabel tag="div" class="confirm-info-message" :code="status.label_name" />
						</BaseAuthConfirmSignup>
						<template v-else>
							<template v-if="!status?.success">
								<div class="reg-row">
									<BaseFormField v-for="item in formFields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
										<BaseCmsLabel v-if="item.name == 'first_name'" tag="div" code="personal_data" class="a-subtitle a-subtitle-personal" />
										<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]" v-interpolation>
											<!-- FIXME - baca error za site da nije potpun -->
											<template v-if="item.name == 'lang' || item.name == 'site'">
												<BaseFormInput :id="'signup-'+item.name" hidden />
											</template>
											<template v-else-if="item.name == 'country'">
												<BaseFormInput :id="'signup-'+item.name" hidden />
												<!-- <input type="hidden" id="field-<?php echo $field; ?>" name="<?php echo $field; ?>" value="<?php echo $country_id; ?>" /> -->
											</template>
											<template v-else-if="item.name == 'loyalty_request'">
												<span class="reg-loyalty_request">
													<BaseCmsLabel tag="h2" class="a-loyalty" code="auth_loyalty_title_nocard" />
													<span v-for="(option, index) in item.options" :key="index" :class="['field-loyalty', 'field-loyalty_request', 'field-loyalty_request-'+option.key]">
														<BaseFormInput mode="custom" :name="item.name" :checked="option.selected" :id="'field-loyalty_request-'+option.key" :value="option.key" />
														<label :for="'field-loyalty_request-'+option.key">{{option.title}}</label>
														<span class="field-loyalty-note">
															<BaseCmsLabel tag="span" class="field-loyalty-note-inner" :code="item.name+'_'+option.key+'_note'" default="" />
															<template v-if="option.key == 'e'">
																<BaseFormField v-if="loyaltyCodeField" :item="loyaltyCodeField" v-slot="{errorMessage}">
																	<BaseFormInput :id="loyaltyCodeField.name" class="field_string" :maxlength="loyaltyCodeField.maxLength" :size="loyaltyCodeField.maxLength" :placeholder="labels.get('loyalty_code')" />
																	<span class="error" v-show="errorMessage" v-html="errorMessage" />
																</BaseFormField>
															</template>
														</span>
													</span>
												</span>
											</template>
											<template v-else-if="item.name == 'birthday'">
												<BaseCmsLabel v-if="item.name == 'birthday'" class="birthday-note" tag="span" code="birthday_note" />
												<span class="birthday-field-cnt">
													<UiDatePicker :minDate="'01.01.1900'" :maxDate="new Date().toLocaleDateString('hr').replace(/\s+/g, '')" :uid="item.name" v-slot="{selectedDate}">
														<BaseFormInput hidden readonly :value="selectedDate ? selectedDate.toLocaleDateString('hr').replace(/\s+/g, '') : ''" />
														<label :for="`dp-input-${item.name}`" class="label-datepicker"><BaseCmsLabel :code="item.name" /><span v-if="item.validation?.length && item.validation.some(item => item.type === 'not_empty')"> *</span></label>
													</UiDatePicker>
												</span>
											</template>
											<template v-if="item.name == 'first_name'">
												<BaseFormInput :id="'signup-'+item.name" type="datepicker" />
											</template>
											<BaseFormInput v-else :id="'signup-'+item.name" />
											<BaseCmsLabel v-if="item.name != 'loyalty_request' && item.name != 'birthday' && item.name != 'site' && item.name != 'lang' && item.name != 'country'" tag="label" :for="'signup-'+item.name" :code="item.name" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</p>
									</BaseFormField>
								</div>

								<!-- <div class="reg-row">
									<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
										<p v-if="item.name == 'email' || item.name == 'password' || item.name == 'password_confirm'" class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]" v-interpolation>
											<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
											<BaseFormInput :id="item.name" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</p>
									</BaseFormField>
								</div>

								<div class="reg-row reg-first_name">
									<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
										<BaseCmsLabel v-if="item.name == 'first_name'" tag="div" code="personal_data" class="a-subtitle a-subtitle-personal"></BaseCmsLabel>
										<template v-if="item.name != 'birthday'">
											<p v-if="!specialFields.includes(item.name)" class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]" v-interpolation>
												<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
												<BaseFormInput :id="item.name" />
												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</p>
										</template>
										<template v-else>
											<span class="birthday-cnt">
												<BaseCmsLabel v-if="item.name == 'birthday'" class="birthday-note" tag="div" code="birthday_note" />
												<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
													<UiDatePicker :minDate="'01.01.1900'" :maxDate="new Date().toLocaleDateString('hr').replace(/\s+/g, '')" :uid="item.name" v-slot="{selectedDate}">
														<BaseFormInput hidden readonly :value="selectedDate ? selectedDate.toLocaleDateString('hr').replace(/\s+/g, '') : ''" />
														<label :for="`dp-input-${item.name}`" class="label-datepicker"><BaseCmsLabel :code="item.name" /><span v-if="item.validation?.length && item.validation.some(item => item.type === 'not_empty')"> *</span></label>
													</UiDatePicker>
												</p>
											</span>
										</template>
									</BaseFormField>
								</div>

								<div class="reg-row reg-newsletter">
									<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
										<p v-if="item.name == 'newsletter' || item.name == 'accept_terms'" class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]" v-interpolation>
											<BaseFormInput :id="item.name" />
											<BaseCmsLabel v-interpolation tag="label" :for="item.name" :code="item.name" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</p>
									</BaseFormField>
								</div> -->

								<p class="submit">
									<button class="btn btn-signup g-recaptcha" type="submit"><BaseCmsLabel code="confirm_signup" tag="span" /></button>
									<!-- <button class="btn btn-signup" data-sitekey="<?php echo App::config_single('recaptcha_site_key_' . $info['site_id']); ?>" data-callback='onSubmit' data-action='submit' data-before_submit type="submit"><BaseCmsLabel code="confirm_signup" tag="span" /></button> -->
								</p>
							</template>
							<div v-else>
								<BaseCmsLabel tag="div" class="global-success" :code="status.data.label_name" />
							</div>
						</template>
					</div>
				</BaseAuthSignupForm>
			</template>
		</AuthLoginRegisterLayout>
	</BaseCmsPage>
</template>

<script setup>
	const specialFields = ['email', 'password', 'password_confirm', 'newsletter', 'accept_terms'];
	const {getAppUrl} = useApiRoutes();
	const labels = useLabels();

	const formFields = ref([]);
	const loyaltyCodeField = ref(null);

	function onFormLoad({fields}) {
		fields.forEach(field => {
			if(field.name == 'loyalty_request') {
				let newfield = field;
				newfield.type = 'radio';
				newfield.value = '';
				newfield.options = [
					{
						"title": "Nemam karticu, želim novu",
						"key": "n",
						"code": null,
						"selected": true
					},
					{
						"title": "Imam karticu",
						"key": "e",
						"code": null,
						"selected": false
					},
					{
						"title": "Zasad ne želim karticu",
						"key": "",
						"code": null,
						"selected": false
					}
				]
				formFields.value.push(newfield);
			}
			else if(field.name == 'loyalty_code') {
				loyaltyCodeField.value = field;

				const maxLengthItem = field.validation?.find(item => item.type === 'max_length');
				loyaltyCodeField.value.maxLength = maxLengthItem?.value ? maxLengthItem?.value : 45;
			} else {
				formFields.value.push(field);
			}
		})
	}
</script>

<style scoped lang="less">
	:deep(.field){
		[hidden]{display: none;}
	}
	.field-lang,.field-site,.field-country{padding-bottom: 0;}

	.field-warehouse_location{
		display: flex; flex-flow: column-reverse; padding-bottom: 30px;
		label{padding-top: 0; padding-bottom: 10px;}
	}

	.field-birthday{padding-bottom: 32px; width: auto; display: flex; align-items: center;}
	.birthday-field-cnt{width: 170px; position: relative; flex-shrink: 0;
		label{left: 22px;}
	}
	.birthday-note{
		font-size: 14px; line-height: 18px; position: relative; padding-left: 44px; padding-right: 10px;
		&:before{.pseudo(auto,auto); font: 35px/35px @fonti; color: @lightGreen; left: 0; top: 0; .icon-surprise}
	}
</style>
