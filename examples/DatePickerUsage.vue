<template>
	<div class="date-picker-examples">
		<h2>Date Picker Usage Examples</h2>
		
		<!-- Example 1: Basic usage with BaseFormField -->
		<div class="example">
			<h3>1. Basic Form Field with Date Type</h3>
			<BaseForm v-slot="{values}">
				<BaseFormField :item="dateField" v-slot="{errorMessage, floatingLabel, isTouched}">
					<p class="field" :class="['field-' + dateField.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched}]">
						<BaseFormInput :id="dateField.name" />
						<label :for="dateField.name">
							<BaseCmsLabel :code="dateField.name" />
							<span v-if="dateField.validation?.length"> *</span>
						</label>
						<span class="error" v-show="errorMessage" v-html="errorMessage" />
					</p>
				</BaseFormField>
				<pre>Form Values: {{ JSON.stringify(values, null, 2) }}</pre>
			</BaseForm>
		</div>

		<!-- Example 2: Direct DatePicker component usage -->
		<div class="example">
			<h3>2. Direct DatePicker Component</h3>
			<BaseFormDatePicker 
				v-model="selectedDate"
				name="direct_date"
				placeholder="Select a date"
				:min-date="'01.01.2020'"
				:max-date="new Date()"
				@change="onDateChange"
			/>
			<p>Selected Date: {{ selectedDate }}</p>
		</div>

		<!-- Example 3: DatePicker with custom date range -->
		<div class="example">
			<h3>3. DatePicker with Custom Range (Birth Date)</h3>
			<BaseFormDatePicker 
				v-model="birthDate"
				name="birth_date"
				placeholder="Select birth date"
				:min-date="'01.01.1900'"
				:max-date="new Date()"
				:clearable="false"
			/>
			<p>Birth Date: {{ birthDate }}</p>
		</div>
	</div>
</template>

<script setup>
	// Example data for form field
	const dateField = ref({
		name: 'event_date',
		type: 'date',
		value: '',
		validation: [
			{
				type: 'not_empty',
				value: null,
				error: 'error_not_empty'
			}
		]
	});

	// Direct component usage
	const selectedDate = ref('');
	const birthDate = ref('');

	function onDateChange(newDate) {
		console.log('Date changed:', newDate);
	}
</script>

<style lang="less" scoped>
	.date-picker-examples {
		padding: 20px;
		max-width: 800px;
		margin: 0 auto;
		
		.example {
			margin-bottom: 40px;
			padding: 20px;
			border: 1px solid #ddd;
			border-radius: 8px;
			
			h3 {
				margin-top: 0;
				color: #333;
			}
			
			.field {
				position: relative;
				margin-bottom: 20px;
				
				label {
					position: absolute;
					top: 12px;
					left: 12px;
					transition: all 0.3s ease;
					pointer-events: none;
					color: #666;
				}
				
				&.ffl-floated label {
					top: -8px;
					left: 8px;
					font-size: 12px;
					background: white;
					padding: 0 4px;
					color: #333;
				}
				
				&.err {
					:deep(.dp__input) {
						border-color: #e74c3c;
					}
				}
				
				&.success {
					:deep(.dp__input) {
						border-color: #27ae60;
					}
				}
				
				.error {
					color: #e74c3c;
					font-size: 12px;
					margin-top: 4px;
					display: block;
				}
			}
			
			pre {
				background: #f8f9fa;
				padding: 10px;
				border-radius: 4px;
				font-size: 12px;
				overflow-x: auto;
			}
			
			p {
				margin: 10px 0;
				font-weight: bold;
			}
		}
	}
</style>
